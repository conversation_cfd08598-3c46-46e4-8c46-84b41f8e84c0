import logging
import time
from typing import Dict, Optional, List, Any, Union
from datetime import datetime, timedelta, timezone

from typing import Union
from data.market_data import MarketDataProvider
from data.hybrid_market_data import HybridMarketDataWrapper
from execution.oanda_client import OandaClient
from strategies.base_strategy import BaseStrategy
from utils.logger import Logger
from utils.error_messages import get_user_friendly_error_message
from data.market_data import MIN_CANDLE_COUNT
from execution.firebase_client import FirebaseClient
from execution.oanda_types import OandaExecuteTradeResponseSuccess, Trade
from core.trading_engine_types import TradeType, TradeExecutionResponse, TradeHistoryRow, TradeStatus
from utils.candle_builder import CandleBuilder

# Optional import for health client
try:
    from utils.health_client import HealthClient
    HEALTH_CLIENT_AVAILABLE = True
except ImportError:
    HEALTH_CLIENT_AVAILABLE = False

class TradingEngine:
    """Main trading engine that coordinates strategy execution, market data, and trade execution."""

    def __init__(
        self,
        strategy: BaseStrategy,
        market_data_provider: Union[MarketDataProvider, HybridMarketDataWrapper],
        oanda_client: OandaClient,
        user_id: str,
        strategy_id: str,
        firebase_client: FirebaseClient,
        health_client: Optional[Any] = None
    ):
        """
        Initialize the trading engine.

        Args:
            strategy (BaseStrategy): Trading strategy instance
            market_data_provider (MarketDataProvider): Market data provider instance
            oanda_client (OandaClient): OANDA API client instance
            user_id (str): User ID for Firestore
            strategy_id (str): Strategy ID for Firestore
            firebase_client (FirebaseClient): Firebase client instance
            health_client (Optional[Any]): Health client for reporting metrics
        """
        self.strategy = strategy
        self.market_data_provider = market_data_provider
        self.oanda_client = oanda_client
        self.user_id = user_id
        self.strategy_id = strategy_id
        self.firebase_client = firebase_client
        self.health_client = health_client

        # Setup logging
        self.logger = Logger("TradingEngine")

        # Initialize state
        self.last_update_time = None
        self.current_position = None

        # Risk management state
        self.daily_loss = 0.0
        self.total_profit = 0.0
        self.total_loss = 0.0
        self.daily_loss_limit = None
        self.max_position_size_pct = None
        self.stop_loss_pct = None
        self.take_profit_pct = None
        self.risk_percentage_pct = None
        self.runtime_days = None
        self.total_profit_target = None
        self.total_loss_limit = None
        self.avoid_high_spread = True  # Default to True for safety
        self.start_time = datetime.now(timezone.utc)

        # Performance metrics
        self.metrics = {
            'update_cycles': 0,
            'trades_executed': 0,
            'trades_closed': 0,
            'execution_times': {}
        }

        # Store current thinking data for entry conditions
        self.current_thinking_data = None

        # Initialize risk management parameters from strategy config
        self._initialize_risk_management()
        self.trade_history: List[Dict[str, Any]] = []
        self.current_positions = {}

        # Track open trades for automatic closure detection
        self.previous_open_trades: List[TradeHistoryRow] = []

        # Initialize candle builder for real-time processing
        self.candle_builder = CandleBuilder(
            symbol=self.strategy.instrument,
            timeframe=self.strategy.timeframe
        )

        # Trade-bot state management
        self.state = "INITIALIZING"  # INITIALIZING → BACKFILLING → READY → TRADING
        self.initialization_complete = False
        self.logger.log_info(f"Initialized candle builder for {self.strategy.instrument} {self.strategy.timeframe}")

    def _filter_trades_by_instrument(self, trades: List[TradeHistoryRow]) -> List[TradeHistoryRow]:
        """
        Filter trades to only include those for the current strategy's instrument.

        Args:
            trades: List of trades to filter

        Returns:
            List of trades that match the current strategy's instrument
        """
        if not trades:
            return []

        current_instrument = self.strategy.instrument
        self.logger.log_info(f"🔍 Filtering {len(trades)} trades for instrument: {current_instrument}")

        # Handle both formats: EUR/USD and EUR_USD
        current_instrument_alt = current_instrument.replace('/', '_') if '/' in current_instrument else current_instrument.replace('_', '/')

        filtered_trades = []
        for trade in trades:
            trade_instrument = trade.instrument
            # Check both formats to be safe
            if trade_instrument == current_instrument or trade_instrument == current_instrument_alt:
                filtered_trades.append(trade)
                self.logger.log_info(f"✅ Including trade {trade.tradeID} for {trade_instrument}")
            else:
                self.logger.log_info(f"🚫 Filtering out trade {trade.tradeID} for {trade_instrument} (current strategy is for {current_instrument})")

        self.logger.log_info(f"🔍 Filtered result: {len(filtered_trades)}/{len(trades)} trades match instrument {current_instrument}")
        return filtered_trades

    def _initialize_risk_management(self):
        """
        Initialize risk management parameters from strategy configuration.

        Priority order:
        1. Strategy-defined stop-loss and take-profit values
        2. Risk management configuration values
        3. Default values
        """
        try:
            # Get strategy configuration
            strategy_config = getattr(self.strategy, 'config', {})

            # Get risk management configuration (try both camelCase and snake_case)
            risk_config = strategy_config.get('riskManagement', strategy_config.get('risk_management', {}))
            if not risk_config:
                self.logger.log_info("No risk management configuration found, using defaults")

            # Check if strategy has stop-loss and take-profit defined
            has_strategy_stop_loss = hasattr(self.strategy, 'stop_loss') and self.strategy.stop_loss
            has_strategy_take_profit = hasattr(self.strategy, 'take_profit') and self.strategy.take_profit

            if has_strategy_stop_loss:
                self.logger.log_info(f"Using strategy-defined stop-loss: {self.strategy.stop_loss}")

            if has_strategy_take_profit:
                self.logger.log_info(f"Using strategy-defined take-profit: {self.strategy.take_profit}")

            # Parse risk management parameters with defaults
            self.logger.log_info("Using new risk management structure")

            # Extract risk percentage
            if hasattr(self.strategy, 'risk_percentage') and self.strategy.risk_percentage is not None:
                risk_percentage_str = self.strategy.risk_percentage
                self.risk_percentage_pct = self._parse_percentage(risk_percentage_str, 1.0)
                self.logger.log_info(f"Using risk percentage: {self.risk_percentage_pct}%")
            else:
                self.risk_percentage_pct = 1.0
                self.logger.log_info(f"Using default risk percentage: {self.risk_percentage_pct}%")

            # Extract risk reward ratio and calculate take profit
            if hasattr(self.strategy, 'risk_reward_ratio') and self.strategy.risk_reward_ratio is not None:
                risk_reward_ratio = float(self.strategy.risk_reward_ratio)
                self.logger.log_info(f"Using risk reward ratio: {risk_reward_ratio}")
            else:
                risk_reward_ratio = 2.0
                self.logger.log_info(f"Using default risk reward ratio: {risk_reward_ratio}")

            # Extract stop loss method
            if hasattr(self.strategy, 'stop_loss_method'):
                stop_loss_method = self.strategy.stop_loss_method
                self.logger.log_info(f"Using stop loss method: {stop_loss_method}")

                # Handle different stop loss methods
                if stop_loss_method == "fixed":
                    if hasattr(self.strategy, 'fixed_pips') and self.strategy.fixed_pips is not None:
                        fixed_pips = float(self.strategy.fixed_pips)
                        self.logger.log_info(f"Using fixed pips: {fixed_pips}")
                        # For fixed pips, we'll use the strategy's position sizing calculation
                        # Set a default percentage for display purposes, but actual position sizing
                        # will be handled by the strategy's get_position_size method
                        self.stop_loss_pct = fixed_pips  # Store pips value directly
                        stop_loss_unit = "pips"
                    else:
                        self.stop_loss_pct = 2.0  # Default to 2%
                        stop_loss_unit = "percentage"
                elif stop_loss_method == "indicator":
                    # For indicator-based stop loss, use a default percentage for now
                    self.stop_loss_pct = 2.0
                    stop_loss_unit = "percentage"
                elif stop_loss_method == "risk":
                    # For risk-based stop loss, use a default percentage for now
                    self.stop_loss_pct = 2.0
                    stop_loss_unit = "percentage"
                else:
                    # Default to percentage-based stop loss
                    self.stop_loss_pct = 2.0
                    stop_loss_unit = "percentage"
            else:
                # Default to percentage-based stop loss
                self.stop_loss_pct = 2.0
                stop_loss_unit = "percentage"

            # Calculate take profit based on risk reward ratio
            self.take_profit_pct = self.stop_loss_pct * risk_reward_ratio
            take_profit_unit = stop_loss_unit

            self.logger.log_info(f"Calculated take profit: {self.take_profit_pct}% based on risk reward ratio: {risk_reward_ratio}")

            # Parse max daily loss from risk config
            max_daily_loss_str = risk_config.get('maxDailyLoss', '5%')
            self.daily_loss_limit = self._parse_percentage(max_daily_loss_str, 5.0)

            # Set default values for other risk management parameters (will be overridden below)
            self.max_position_size_pct = 10.0  # Will be overridden from risk_config
            self.runtime_days = self.strategy.runtime if hasattr(self.strategy, 'runtime') else 7
            self.total_profit_target = 20.0  # Will be overridden from risk_config
            self.total_loss_limit = 10.0  # Will be overridden from risk_config
            self.avoid_high_spread = self.strategy.avoid_high_spread if hasattr(self.strategy, 'avoid_high_spread') else True

            # Get current price for unit conversion if needed
            current_price = None
            if stop_loss_unit != "percentage" or take_profit_unit != "percentage":
                try:
                    # Get latest price for the instrument
                    market_data = self.market_data_provider.get_candles(
                        symbol=self.strategy.instrument,
                        timespan=self.strategy.timeframe,
                        count=1
                    )
                    if market_data["status"] == "success" and market_data["candles"]:
                        current_price = market_data["candles"][-1]["close"]
                except Exception as e:
                    self.logger.log_error(e, "Error getting current price for unit conversion")

            # The stop_loss_pct and take_profit_pct are already set above based on the stop loss method
            # No need to convert them again

            self.logger.log_info(f"Final stop-loss: {self.stop_loss_pct}%, take-profit: {self.take_profit_pct}%")

            # Risk percentage is already set above

            # Default runtime: 7 days
            self.runtime_days = int(risk_config.get('runtime', 7))

            # Parse max position size from risk config (this was missing!)
            max_position_size_str = risk_config.get('maxPositionSize', '10%')
            self.max_position_size_pct = self._parse_percentage(max_position_size_str, 10.0)

            # Default total profit target: 20% of account balance
            total_profit_target_str = risk_config.get('totalProfitTarget', '20%')
            self.total_profit_target = self._parse_percentage(total_profit_target_str, 20.0)

            # Default total loss limit: 10% of account balance
            total_loss_limit_str = risk_config.get('totalLossLimit', '10%')
            self.total_loss_limit = self._parse_percentage(total_loss_limit_str, 10.0)

            # Parse avoid high spread setting
            self.avoid_high_spread = risk_config.get('avoidHighSpread', True)

            self.logger.log_info(f"✅ Risk management parsed from config: "
                               f"maxDailyLoss='{max_daily_loss_str}' → {self.daily_loss_limit}%, "
                               f"maxPositionSize='{max_position_size_str}' → {self.max_position_size_pct}%, "
                               f"totalProfitTarget='{total_profit_target_str}' → {self.total_profit_target}%, "
                               f"totalLossLimit='{total_loss_limit_str}' → {self.total_loss_limit}%, "
                               f"avoidHighSpread={self.avoid_high_spread}")

            self.logger.log_info(f"Risk management initialized: "
                               f"daily_loss_limit={self.daily_loss_limit}%, "
                               f"max_position_size={self.max_position_size_pct}%, "
                               f"stop_loss={self.stop_loss_pct}%, "
                               f"take_profit={self.take_profit_pct}%, "
                               f"risk_percentage={self.risk_percentage_pct}%, "
                               f"runtime_days={self.runtime_days}, "
                               f"total_profit_target={self.total_profit_target}%, "
                               f"total_loss_limit={self.total_loss_limit}%, "
                               f"avoid_high_spread={self.avoid_high_spread}")

            # Immediately update risk management metrics in Firebase to ensure they're available right away
            self._update_risk_management_metrics()
        except Exception as e:
            self.logger.log_error(e, "Error initializing risk management, using defaults")
            # Set defaults if there's an error
            self.daily_loss_limit = 5.0  # 5% of account balance
            self.max_position_size_pct = 10.0  # 10% of account balance
            self.stop_loss_pct = 2.0  # 2% from entry price
            self.take_profit_pct = 4.0  # 4% from entry price
            self.risk_percentage_pct = 1.0  # 1% of account balance
            self.runtime_days = 7  # 7 days
            self.total_profit_target = 20.0  # 20% of account balance
            self.total_loss_limit = 10.0  # 10% of account balance
            self.avoid_high_spread = True  # Default to True for safety

        # Subscribe to real-time data if using Pub/Sub provider (after all initialization)
        self.logger.log_info("🔍 DEBUG: Checking if market data provider supports real-time subscriptions...")
        if hasattr(self.market_data_provider, 'subscribe_to_symbol'):
            self.logger.log_info(f"📈 Subscribing to real-time data for {self.strategy.instrument}")
            self.market_data_provider.subscribe_to_symbol(self.strategy.instrument)
            self.logger.log_info("✅ Real-time subscription request sent")
        else:
            self.logger.log_info("ℹ️ Market data provider does not support real-time subscriptions")

    def _parse_percentage(self, percentage_str: str, default: float, unit: str = "percentage") -> float:
        """
        Parse a percentage string (e.g., '5%') to a float.

        Args:
            percentage_str (str): Percentage string to parse
            default (float): Default value if parsing fails
            unit (str): Unit of the value (percentage, pips, dollars)

        Returns:
            float: Parsed percentage as a float
        """
        try:
            if isinstance(percentage_str, str):
                # Remove '%' if present and convert to float
                cleaned_str = percentage_str.strip('%')
                return float(cleaned_str)
            elif isinstance(percentage_str, (int, float)):
                return float(percentage_str)
            else:
                return default
        except (ValueError, TypeError):
            return default

    def _convert_to_percentage(self, value: float, unit: str, price: float = None) -> float:
        """
        Convert a value to a percentage based on its unit.

        Args:
            value (float): Value to convert
            unit (str): Unit of the value (percentage, pips, dollars)
            price (float): Current price, needed for pips and dollars conversion

        Returns:
            float: Value as a percentage
        """
        if unit == "percentage":
            return value
        elif unit == "pips" and price is not None:
            # Convert pips to percentage based on current price
            # 1 pip is typically 0.0001 for 4-decimal pairs
            pip_value = 0.0001
            return (value * pip_value / price) * 100
        elif unit == "dollars" and price is not None:
            # Convert dollars to percentage based on account balance
            account_balance = self._get_account_balance()
            if account_balance > 0:
                return (value / account_balance) * 100
            return 0
        else:
            self.logger.log_warning(f"Unknown unit {unit} or missing price for conversion")
            return value

    def _get_account_balance(self) -> float:
        """
        Get the current account balance with retry mechanism.

        Returns:
            float: Current account balance, or 0.0 if all retries fail
        """
        max_retries = 5
        retry_delay = 1.0  # Start with 1 second delay

        for attempt in range(max_retries):
            try:
                self.logger.log_info(f"Attempt {attempt + 1}/{max_retries}: Calling OANDA get_account_summary()")
                account_summary = self.oanda_client.get_account_summary()
                self.logger.log_info(f"Attempt {attempt + 1}/{max_retries}: Received response: {account_summary}")

                if account_summary and "balance" in account_summary:
                    balance = float(account_summary["balance"])
                    self.logger.log_info(f"Attempt {attempt + 1}/{max_retries}: Parsed balance: {balance}")

                    if balance > 0:
                        self.logger.log_info(f"✅ Successfully retrieved account balance: ${balance:.4f}")
                        return balance
                    else:
                        self.logger.log_warning(f"Attempt {attempt + 1}/{max_retries}: Received zero balance from OANDA API: {balance}")
                else:
                    self.logger.log_warning(f"Attempt {attempt + 1}/{max_retries}: Account summary missing or invalid from OANDA API: {account_summary}")

            except Exception as e:
                self.logger.log_error(f"Attempt {attempt + 1}/{max_retries}: Error getting account balance from OANDA API: {e}")

            # If this wasn't the last attempt, wait before retrying
            if attempt < max_retries - 1:
                self.logger.log_info(f"Retrying in {retry_delay:.1f} seconds...")
                time.sleep(retry_delay)
                retry_delay *= 1.5  # Exponential backoff

        # All retries failed
        self.logger.log_error(f"CRITICAL: Failed to get valid account balance after {max_retries} attempts")
        self.logger.log_error("This will cause trading to stop due to daily loss limit protection")
        return 0.0

    def warm_up(self) -> Dict[str, Any]:
        """Warm up the trading engine with historical data to ensure it's ready to go."""
        try:
            self.logger.log_info("Warming up trading engine...")
            # Check market status
            market_status = self.market_data_provider.check_market_status()
            self.logger.log_info(f"Market status: {market_status}")
            if not market_status["is_open"]:
                self.logger.log_info("Market is closed, skipping warm up but allowing initialization")
                # Set state to READY so the bot can be paused by market hours manager
                self.state = "READY"
                self.initialization_complete = True
                return {
                    "status": "market_closed",
                    "message": "Market is closed - bot initialized but will pause until market opens",
                    "market_status": market_status
                }

            # Check if we have trading sessions defined
            trading_sessions = None
            if hasattr(self.strategy, 'trading_session') and self.strategy.trading_session:
                trading_sessions = self.strategy.trading_session
                self.logger.log_info(f"Trading sessions: {trading_sessions}")

                # Check if we're in a trading session
                session_info = self.market_data_provider.market_conditions.is_in_trading_session(trading_sessions)
                self.logger.log_info(f"Trading session info: {session_info}")

                if not session_info["in_session"]:
                    self.logger.log_info("Not in trading session, skipping warm up but allowing initialization")
                    # Set state to READY so the bot can wait for the trading session
                    self.state = "READY"
                    self.initialization_complete = True
                    return {
                        "status": "not_in_session",
                        "message": f"Outside trading session: {', '.join(trading_sessions)} - bot initialized but will wait for session",
                        "session_info": session_info
                    }

            # Get open trades and filter by current instrument
            all_open_trades = self.oanda_client.get_open_trades()
            open_trades = self._filter_trades_by_instrument(all_open_trades)

            if len(open_trades) > 0:
                self.logger.log_error(Exception("Open trades already exist for this instrument"), f"Open trades already exist for {self.strategy.instrument}")
                self.firebase_client.append_user_log(
                    f"‼️ Cannot start trade bot - There are {len(open_trades)} open trades for {self.strategy.instrument}. Please close them before starting the trade bot."
                )
                raise Exception(f"Cannot start trade bot - There are {len(open_trades)} open trades for {self.strategy.instrument}. Please close them before starting the trade bot.")

            # Log info about other instrument trades if any exist
            if len(all_open_trades) > len(open_trades):
                other_trades_count = len(all_open_trades) - len(open_trades)
                self.logger.log_info(f"ℹ️ Found {other_trades_count} open trades for other instruments - these will not affect this {self.strategy.instrument} bot")



            # Check if using PubSub provider and initialize properly
            provider_type = type(self.market_data_provider).__name__
            self.logger.log_info(f"🔍 Market data provider type: {provider_type}")

            if hasattr(self.market_data_provider, 'initialize_with_symbol'):
                # Use proper initialization sequence for PubSub provider
                self.logger.log_info("🚀 Using PubSub provider - initializing with proper sequence")
                market_data = self.market_data_provider.initialize_with_symbol(
                    symbol=self.strategy.instrument,
                    timespan=self.strategy.timeframe,
                    multiplier=1,  # Add explicit multiplier
                    count=MIN_CANDLE_COUNT
                )
            else:
                # Use standard method for other providers
                self.logger.log_info(f"🔄 Using standard get_candles() method for {provider_type}")
                market_data = self.market_data_provider.get_candles(
                    symbol=self.strategy.instrument,
                    timespan=self.strategy.timeframe,
                    count=MIN_CANDLE_COUNT
                )

            if market_data["status"] == "market_data_error":
                error_msg = f"Market data error: {market_data['message']}"
                self.logger.log_error(Exception(error_msg), "Failed to warm up trading engine")
                return {
                    "status": "market_data_error",
                    "message": error_msg
                }

            self.logger.log_info(f"Fetched {len(market_data['candles'])} formatted_candles")

            # Store market data for later use (avoid re-fetching)
            self.last_market_data = market_data

            # Initialize candle builder with 1m candles if available
            if "candle_builder_1m_candles" in market_data:
                candle_builder_1m_candles = market_data["candle_builder_1m_candles"]
                if candle_builder_1m_candles:
                    self.logger.log_info(f"🔧 Initializing candle builder with {len(candle_builder_1m_candles)} 1m candles")
                    for candle in candle_builder_1m_candles:
                        # Add each 1m candle to the candle builder
                        completed_candle = self.candle_builder.add_1m_candle(candle)
                        if completed_candle:
                            self.logger.log_info(f"🎉 Completed {self.strategy.timeframe} candle during initialization: {completed_candle.get('time')}")
                    self.logger.log_info(f"✅ Candle builder initialized with current period 1m candles")
                else:
                    self.logger.log_info(f"ℹ️ No 1m candles needed for candle builder initialization")

            # Validate sequential continuity of candles
            timeframe_minutes = self._get_timeframe_minutes(self.strategy.timeframe)
            if hasattr(self.market_data_provider, 'validate_sequential_candles'):
                if not self.market_data_provider.validate_sequential_candles(market_data['candles'], timeframe_minutes):
                    error_msg = "Sequential validation failed - candles are not properly sequential"
                    self.logger.log_error(Exception(error_msg), "Failed to validate candle sequence")
                    return {
                        "status": "validation_error",
                        "message": error_msg
                    }
                self.logger.log_info("✅ Sequential validation passed")

            # Check if we need to start backfill process for higher timeframes
            if hasattr(self.market_data_provider, 'missing_candle_timestamp') and self.market_data_provider.missing_candle_timestamp:
                self.logger.log_info("🔄 Starting backfill process for missing candle")
                self.state = "BACKFILLING"
                self.market_data_provider.start_backfill_process(self._on_backfill_complete)
            else:
                # No backfill needed, proceed to ready state
                self.state = "READY"
                self.initialization_complete = True

            # Calculate indicators
            indicators = self.strategy.calculate_indicators(market_data["candles"])

            # Debug: Check for None values in indicators
            for name, values in indicators.items():
                none_count = sum(1 for v in values if v is None)
                if none_count > 0:
                    self.logger.log_info(f"📊 Indicator '{name}': {none_count}/{len(values)} values are None (normal for initial periods)")
                else:
                    self.logger.log_info(f"📊 Indicator '{name}': All {len(values)} values calculated successfully")

            # Get account balance using retry mechanism
            account_balance = self._get_account_balance()
            if account_balance <= 0:
                error_msg = "Failed to fetch valid account balance after retries"
                self.logger.log_error(Exception(error_msg), error_msg)
                return {
                    "status": "error",
                    "message": error_msg
                }

            # Create account summary for compatibility
            account_summary = {"balance": str(account_balance)}

            # Format indicators for chart display
            formatted_indicators = {}
            for name, values in indicators.items():
                formatted_indicators[name] = [{
                    "time": candle["time"],
                    "value": float(value) if value is not None else None
                } for candle, value in zip(market_data["candles"], values)]

            self.logger.log_info(f"Warmed up trading engine successfully")
            return {
                "status": "success",
                "message": "Warmed up trading engine successfully",
                "market_data": {
                    "candles": market_data["candles"],
                    "indicators": formatted_indicators
                },
                "account_summary": account_summary
            }

        except Exception as e:
            self.logger.log_error(e, "Error warming up trading engine")
            return {
                "status": "error",
                "message": str(e)
            }

    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe to minutes."""
        if timeframe == "1m":
            return 1
        elif timeframe == "5m":
            return 5
        elif timeframe == "15m":
            return 15
        elif timeframe == "30m":
            return 30
        elif timeframe == "1h":
            return 60
        elif timeframe == "4h":
            return 240
        elif timeframe == "1d":
            return 1440
        else:
            # Try to parse formats like "5m", "15m", etc.
            if timeframe.endswith("m"):
                return int(timeframe[:-1])
            elif timeframe.endswith("h"):
                return int(timeframe[:-1]) * 60
            else:
                return 1  # Default fallback

    def _on_backfill_complete(self, missing_candle):
        """
        Callback when the missing candle is found and backfilled.

        Args:
            missing_candle: The candle data that was missing
        """
        try:
            self.logger.log_info(f"🎉 Backfill complete! Received missing candle: {missing_candle.get('timestamp')}")

            # Add the missing candle to our buffer
            # This should be inserted at the correct position in the candle buffer
            # For now, we'll assume the buffer management handles this

            # Validate sequential continuity again after backfill
            if hasattr(self.last_market_data, 'candles'):
                timeframe_minutes = self._get_timeframe_minutes(self.strategy.timeframe)
                if hasattr(self.market_data_provider, 'validate_sequential_candles'):
                    if self.market_data_provider.validate_sequential_candles(self.last_market_data['candles'], timeframe_minutes):
                        self.logger.log_info("✅ Post-backfill sequential validation passed")
                        self.state = "READY"
                        self.initialization_complete = True
                    else:
                        self.logger.log_error("❌ Post-backfill sequential validation failed")
                        self.state = "ERROR"
                else:
                    # If validation method not available, assume success
                    self.state = "READY"
                    self.initialization_complete = True
            else:
                self.state = "READY"
                self.initialization_complete = True

            self.logger.log_info(f"🚀 Trade-bot state: {self.state} - Ready to start trading!")

        except Exception as e:
            self.logger.log_error(f"❌ Error during backfill completion: {e}")
            self.state = "ERROR"

    def update_with_new_candle(self, completed_candle):
        """
        Update the trading engine with a new completed candle from real-time data.

        Args:
            completed_candle: The new completed candle data
        """
        try:
            # Check if trade-bot is ready for trading
            if self.state != "READY" and self.state != "TRADING":
                self.logger.log_info(f"⏳ Trade-bot not ready for trading (state: {self.state}) - skipping candle update")
                return

            # Update state to TRADING once we start processing candles
            if self.state == "READY":
                self.state = "TRADING"
                self.logger.log_info(f"🚀 Trade-bot state changed to TRADING - starting to process candles")

            self.logger.log_info(f"🔄 Updating trading engine with new real-time candle")

            # For PubSub providers, use the cached market data from warmup and update it
            # This avoids unnecessary API calls since we already have the buffer
            if hasattr(self, 'last_market_data') and self.last_market_data:
                candles = self.last_market_data["candles"].copy()
                self.logger.log_info(f"🔄 Using cached candle buffer with {len(candles)} candles")
            else:
                # Fallback: Get current candles from market data provider
                self.logger.log_warning(f"⚠️ No cached market data available, fetching from provider")
                market_data = self.market_data_provider.get_candles(
                    symbol=self.strategy.instrument,
                    timespan=self.strategy.timeframe,
                    count=MIN_CANDLE_COUNT
                )

                if market_data["status"] != "success":
                    self.logger.log_error(f"❌ Failed to get current candles for real-time update: {market_data.get('message', 'Unknown error')}")
                    return

                candles = market_data["candles"]

            # Add the new candle to the end and remove the oldest (FIFO)
            candles.append(completed_candle)
            if len(candles) > MIN_CANDLE_COUNT:
                candles = candles[-MIN_CANDLE_COUNT:]  # Keep only the latest 1000 candles

            # Update the cached market data for future use
            if hasattr(self, 'last_market_data'):
                self.last_market_data["candles"] = candles

            self.logger.log_info(f"📊 Updated candle buffer: {len(candles)} candles (added new, removed oldest)")

            # Recalculate indicators with updated candles
            indicators = self.strategy.calculate_indicators(candles)
            self.logger.log_info(f"🔄 Recalculated indicators with new candle: {list(indicators.keys())}")

            # Get account balance
            account_balance = self._get_account_balance()
            if account_balance <= 0:
                self.logger.log_warning("⚠️ Failed to get account balance for real-time update")
                return

            # Get open trades
            # Get open trades and filter by current instrument
            all_open_trades = self.oanda_client.get_open_trades()
            open_trades = self._filter_trades_by_instrument(all_open_trades)

            # Update strategy with new data
            strategy_update = self.strategy.update(
                market_data={
                    "candles": candles,
                    "indicators": indicators
                },
                account_balance=account_balance,
                open_trades=open_trades  # Pass filtered open trades to the strategy
            )

            # Calculate trading signals
            signals = strategy_update.get("signals", {})
            if signals:
                self.logger.log_info(f"🚨 Real-time signals generated: {signals}")

                # Execute trades based on real-time signals
                account_summary = {"balance": str(account_balance)}
                current_thinking_data = strategy_update.get("thinking")
                trade_result = self._execute_trade(
                    signals,
                    {"candles": candles, "indicators": indicators},
                    account_summary,
                    open_trades,
                    current_thinking_data
                )

                if trade_result:
                    self.logger.log_info(f"✅ Real-time trade executed: {trade_result}")
                else:
                    self.logger.log_info("ℹ️ Real-time trade execution completed but no trade was placed")
            else:
                self.logger.log_info("ℹ️ No real-time signals generated - continuing to monitor")

            # Update thinking data in Firebase
            thinking_data = strategy_update.get("thinking")
            if thinking_data:
                self.firebase_client.update_thinking_data(thinking_data)

            self.logger.log_info("✅ Real-time candle update completed successfully")

        except Exception as e:
            self.logger.log_error(f"❌ Error updating with new candle: {e}")

    def _check_daily_loss_limit(self, account_balance: float) -> bool:
        """
        Check if the daily loss limit has been exceeded.

        Args:
            account_balance (float): Current account balance

        Returns:
            bool: True if daily loss limit has been exceeded, False otherwise
        """
        if self.daily_loss_limit is None:
            return False

        # Calculate daily loss limit in absolute terms
        daily_loss_limit_abs = account_balance * (self.daily_loss_limit / 100.0)

        # Check if daily loss exceeds the limit
        if self.daily_loss >= daily_loss_limit_abs:
            self.logger.log_warning(
                f"Daily loss limit exceeded: {self.daily_loss:.2f} > {daily_loss_limit_abs:.2f}"
            )
            self.firebase_client.append_user_log(
                f"⚠️ Daily loss limit of {self.daily_loss_limit}% exceeded. Trading paused for today."
            )
            return True

        return False

    def _check_runtime_expiration(self) -> bool:
        """
        Check if the bot's runtime has expired.

        Returns:
            bool: True if runtime has expired, False otherwise
        """
        if self.runtime_days is None:
            return False

        # Calculate end time
        end_time = self.start_time + timedelta(days=self.runtime_days)

        # Check if current time exceeds end time
        if datetime.now(timezone.utc) > end_time:
            self.logger.log_warning(
                f"Runtime of {self.runtime_days} days has expired. Started on {self.start_time}, ended on {end_time}"
            )
            self.firebase_client.append_user_log(
                f"⏰ Bot runtime of {self.runtime_days} days has expired. Bot will stop."
            )
            return True

        return False

    def _check_total_profit_target(self, account_balance: float) -> bool:
        """
        Check if the total profit target has been reached.

        Args:
            account_balance (float): Current account balance

        Returns:
            bool: True if total profit target has been reached, False otherwise
        """
        if self.total_profit_target is None:
            return False

        # Calculate total profit target in absolute terms
        total_profit_target_abs = account_balance * (self.total_profit_target / 100.0)

        # Check if total profit exceeds the target
        if self.total_profit >= total_profit_target_abs:
            self.logger.log_info(
                f"Total profit target reached: {self.total_profit:.2f} >= {total_profit_target_abs:.2f}"
            )
            self.firebase_client.append_user_log(
                f"✅ Total profit target of {self.total_profit_target}% reached. Bot will stop."
            )
            return True

        return False

    def _check_total_loss_limit(self, account_balance: float) -> bool:
        """
        Check if the total loss limit has been exceeded.

        Args:
            account_balance (float): Current account balance

        Returns:
            bool: True if total loss limit has been exceeded, False otherwise
        """
        if self.total_loss_limit is None:
            return False

        # Calculate total loss limit in absolute terms
        total_loss_limit_abs = account_balance * (self.total_loss_limit / 100.0)

        # Check if total loss exceeds the limit
        if self.total_loss >= total_loss_limit_abs:
            self.logger.log_warning(
                f"Total loss limit exceeded: {self.total_loss:.2f} > {total_loss_limit_abs:.2f}"
            )
            self.firebase_client.append_user_log(
                f"⛔ Total loss limit of {self.total_loss_limit}% exceeded. Bot will stop."
            )
            return True

        return False

    def _update_daily_loss(self, realized_pl: float, update_metrics: bool = False):
        """
        Update the daily loss tracker with a new realized P&L value.

        Args:
            realized_pl (float): Realized P&L from a closed trade
            update_metrics (bool, optional): Whether to update risk management metrics. Defaults to False.
        """
        # If P&L is negative, add it to the daily loss
        if realized_pl < 0:
            self.daily_loss += abs(realized_pl)
            self.logger.log_info(f"Updated daily loss: {self.daily_loss:.2f}")

            # Update risk management metrics in Firebase if requested
            if update_metrics:
                self._update_risk_management_metrics()

    def _update_risk_management_metrics(self):
        """
        Update risk management metrics in Firebase.
        This includes daily loss, total profit, total loss, and progress towards targets.
        """
        try:
            # Calculate runtime progress
            now = datetime.now(timezone.utc)
            elapsed_days = (now - self.start_time).total_seconds() / (24 * 60 * 60)  # Convert to days
            runtime_progress = min(100, (elapsed_days / self.runtime_days) * 100) if self.runtime_days > 0 else 0
            days_remaining = max(0, self.runtime_days - elapsed_days)

            # Get account balance to calculate absolute values using retry mechanism
            account_balance = self._get_account_balance()

            # Calculate absolute values for targets and limits
            daily_loss_limit_abs = account_balance * (self.daily_loss_limit / 100.0) if account_balance > 0 else 0
            max_position_size_abs = account_balance * (self.max_position_size_pct / 100.0) if account_balance > 0 else 0
            total_profit_target_abs = account_balance * (self.total_profit_target / 100.0) if account_balance > 0 else 0
            total_loss_limit_abs = account_balance * (self.total_loss_limit / 100.0) if account_balance > 0 else 0

            self.logger.log_info(f"Calculated absolute values: "
                               f"daily_loss_limit_abs={daily_loss_limit_abs}, "
                               f"max_position_size_abs={max_position_size_abs}, "
                               f"total_profit_target_abs={total_profit_target_abs}, "
                               f"total_loss_limit_abs={total_loss_limit_abs}")

            # Create risk management metrics dictionary
            risk_metrics = {
                "dailyLoss": self.daily_loss,
                "totalProfit": self.total_profit,
                "totalLoss": self.total_loss,
                "runtimeProgress": runtime_progress,
                "elapsedDays": elapsed_days,
                "daysRemaining": days_remaining,
                "accountBalance": account_balance,
                "dailyLossLimitAbs": daily_loss_limit_abs,
                "maxPositionSizeAbs": max_position_size_abs,
                "totalProfitTargetAbs": total_profit_target_abs,
                "totalLossLimitAbs": total_loss_limit_abs,
                "lastUpdated": now
            }

            self.logger.log_info(f"Updating risk management metrics: "
                               f"totalProfit={self.total_profit}, "
                               f"totalLoss={self.total_loss}, "
                               f"dailyLoss={self.daily_loss}, "
                               f"accountBalance={account_balance}")

            # Add risk management parameters using the new structure
            risk_params = {
                # New parameters
                "riskPercentage": f"{self.risk_percentage_pct}%" if self.risk_percentage_pct is not None else None,
                "riskRewardRatio": self.strategy.risk_reward_ratio if hasattr(self.strategy, 'risk_reward_ratio') else None,
                "stopLossMethod": self.strategy.stop_loss_method if hasattr(self.strategy, 'stop_loss_method') else "fixed",
                "fixedPips": self.strategy.fixed_pips if hasattr(self.strategy, 'fixed_pips') and self.strategy.fixed_pips is not None else None,
                "indicatorBasedSL": self.strategy.indicator_based_sl if hasattr(self.strategy, 'indicator_based_sl') else None,
                "lotSize": self.strategy.lot_size if hasattr(self.strategy, 'lot_size') else None,

                # Additional parameters
                "maxDailyLoss": f"{self.daily_loss_limit}%" if self.daily_loss_limit is not None else None,
                "maxPositionSize": f"{self.max_position_size_pct}%" if self.max_position_size_pct is not None else None,
                "totalProfitTarget": f"{self.total_profit_target}%" if self.total_profit_target is not None else None,
                "totalLossLimit": f"{self.total_loss_limit}%" if self.total_loss_limit is not None else None,
                "runtime": self.runtime_days,
                "startTime": self.start_time.isoformat(),
                "avoidHighSpread": self.avoid_high_spread  # Include the avoid_high_spread parameter
            }

            # Log the risk parameters for debugging
            self.logger.log_info(f"Risk parameters being saved to Firebase: {risk_params}")

            # Combine metrics and parameters
            risk_management_data = {
                "metrics": risk_metrics,
                "parameters": risk_params
            }

            # Update in Firebase
            self.firebase_client.update_risk_management(risk_management_data)

            self.logger.log_info("Updated risk management metrics in Firebase")
        except Exception as e:
            self.logger.log_error(e, "Error updating risk management metrics in Firebase")

    def _process_close_signal(self, signals: Dict[str, Any], market_data: Dict[str, Any], account_summary: Dict[str, Any], open_trades: List[TradeHistoryRow]) -> Optional[TradeExecutionResponse]:
        """
        Process a CLOSE signal and check for new entry signals in the same cycle.

        Args:
            signals (Dict[str, Any]): Trading signals from strategy
            market_data (Dict[str, Any]): Current market data
            account_summary (Dict[str, Any]): Account summary from OANDA
            open_trades (List[TradeHistoryRow]): List of open trades

        Returns:
            Optional[TradeExecutionResponse]: Trade execution results if successful
        """
        # Get current price
        current_price = float(market_data["candles"][-1]["close"])

        # Close the current position
        self.logger.log_info(f"Closing trade based on exit signal: {signals['reason']}")
        self.logger.log_info(f"Open trade details: ID={open_trades[0].tradeID}, instrument={open_trades[0].instrument}, units={open_trades[0].units}")

        # Format the instrument to match OANDA's format (EUR_USD instead of EUR/USD)
        instrument = self.strategy.instrument
        formatted_instrument = instrument.replace('/', '_')
        self.logger.log_info(f"Using instrument {instrument} (formatted as {formatted_instrument}) for closing trade")

        trade_result = self.oanda_client.execute_trade(
            instrument=instrument,
            units=float(open_trades[0].units),  # Use the same units as the open position
            type=TradeType.CLOSE,
            price=current_price
        )

        if trade_result:
            # Increment trades closed counter
            self.metrics['trades_closed'] += 1

            self.firebase_client.append_user_log(
                f"🟢 Trade executed: CLOSE order placed - {signals['reason']}"
            )

            # Report trade execution metrics
            if self.health_client:
                self.health_client.update_dependency("trade_execution", "healthy", "Successfully closed trade")

            # Update trackers if the trade had a P&L
            # Check if trade_result is a dict or a TradeExecutionResponse object
            realized_pl = 0
            if isinstance(trade_result, dict) and 'realizedPL' in trade_result:
                realized_pl = trade_result['realizedPL']
            elif hasattr(trade_result, 'pl'):
                realized_pl = trade_result.pl

            if realized_pl != 0:
                self.logger.log_info(f"Trade closed with realized P&L: {realized_pl}")

                # Update daily loss tracker if negative P&L (without updating metrics yet)
                if realized_pl < 0:
                    self._update_daily_loss(realized_pl, update_metrics=False)

                # Update total profit and loss trackers (without updating metrics yet)
                self._update_total_pnl(realized_pl, update_metrics=False)

                # Now update risk management metrics once - this is more efficient
                # This ensures the frontend gets the latest values with a single update
                self._update_risk_management_metrics()

                # Update the trade history in Firebase
                # Create a closed trade history row
                closed_trade = open_trades[0]
                # Get halfSpreadCost from the trade result if available
                half_spread_cost = None
                if isinstance(trade_result, dict) and 'halfSpreadCost' in trade_result:
                    half_spread_cost = trade_result['halfSpreadCost']
                elif hasattr(trade_result, 'halfSpreadCost'):
                    half_spread_cost = trade_result.halfSpreadCost

                closed_trade_history_row = TradeHistoryRow(
                    tradeID=closed_trade.tradeID,
                    type=closed_trade.type,
                    status=TradeStatus.CLOSED,
                    instrument=closed_trade.instrument,
                    price=closed_trade.price,
                    openTime=closed_trade.openTime,
                    units=closed_trade.units,
                    initialMarginRequired=closed_trade.initialMarginRequired,
                    unrealizedPL=0,  # No longer unrealized
                    realizedPL=realized_pl,
                    closeTime=datetime.now(timezone.utc),
                    takeProfitPrice=closed_trade.takeProfitPrice,
                    stopLossPrice=closed_trade.stopLossPrice,
                    halfSpreadCost=half_spread_cost,
                    commission=closed_trade.commission  # Pass through the original commission
                )

                # Update the existing trade in the trade history in Firebase
                self.logger.log_info(f"Updating trade history with closed trade: {closed_trade_history_row}")
                self.firebase_client.update_open_trades([closed_trade_history_row])

            # After closing the trade, check for new entry signals in the same cycle
            self.logger.log_info("Checking for new entry signals after closing trade...")

            # Refresh the list of open trades to confirm the trade was closed
            # First, clear the cache to ensure we get fresh data
            if hasattr(self.oanda_client, 'cache') and 'open_trades' in self.oanda_client.cache:
                self.oanda_client.cache['open_trades']['timestamp'] = 0  # Force cache refresh

            refreshed_open_trades = self.oanda_client.get_open_trades()

            # Check if the specific trade we just closed is no longer in the list
            closed_trade_id = closed_trade_history_row.tradeID if 'closed_trade_history_row' in locals() else None
            trade_still_open = next((t for t in refreshed_open_trades if t.tradeID == closed_trade_id), None) if closed_trade_id else None

            if not trade_still_open:
                # Trade was successfully closed, now check for new entry signals
                new_signals = self.strategy.calculate_signals(market_data=market_data)

                if new_signals and new_signals.get("action") in ["BUY", "SELL"]:
                    self.logger.log_info(f"New entry signal detected after closing trade: {new_signals['action']} - {new_signals['reason']}")

                    # Add position size to the new signals
                    new_signals["position_size"] = self.strategy.get_position_size(
                        account_balance=float(account_summary["balance"]),
                        current_price=current_price
                    )

                    # Execute the new trade
                    return self._execute_trade(new_signals, market_data, account_summary, [])
                else:
                    self.logger.log_info("No new entry signals after closing trade")
            else:
                if closed_trade_id:
                    self.logger.log_warning(f"Trade {closed_trade_id} appears to still be open in Oanda, cannot check for new entry signals")
                else:
                    self.logger.log_warning("Cannot verify if trade was successfully closed, skipping new entry signal check")

        return trade_result

    def _update_total_pnl(self, realized_pl: float, update_metrics: bool = False):
        """
        Update the total profit and loss trackers with a new realized P&L value.

        Args:
            realized_pl (float): Realized P&L from a closed trade
            update_metrics (bool, optional): Whether to update risk management metrics. Defaults to False.
        """
        if realized_pl > 0:
            self.total_profit += realized_pl
            self.logger.log_info(f"Updated total profit: {self.total_profit:.2f}")
        elif realized_pl < 0:
            self.total_loss += abs(realized_pl)
            self.logger.log_info(f"Updated total loss: {self.total_loss:.2f}")

        # Update risk management metrics in Firebase if requested
        if update_metrics:
            self._update_risk_management_metrics()

    def _track_execution_time(self, operation: str, start_time: float) -> None:
        """
        Track execution time for an operation.

        Args:
            operation (str): Operation name
            start_time (float): Start time from time.time()
        """
        import time
        execution_time = time.time() - start_time

        if operation not in self.metrics['execution_times']:
            self.metrics['execution_times'][operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0
            }

        metrics = self.metrics['execution_times'][operation]
        metrics['count'] += 1
        metrics['total_time'] += execution_time
        metrics['min_time'] = min(metrics['min_time'], execution_time)
        metrics['max_time'] = max(metrics['max_time'], execution_time)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the trading engine.

        Returns:
            Dict[str, Any]: Performance metrics including:
                - update_cycles: Number of update cycles run
                - trades_executed: Number of trades executed
                - trades_closed: Number of trades closed
                - execution_times: Execution time statistics for various operations
                - oanda_metrics: Performance metrics from the OANDA client
                - firebase_metrics: Performance metrics from the Firebase client
        """
        metrics = self.metrics.copy()

        # Calculate average execution times
        for operation, data in metrics['execution_times'].items():
            if data['count'] > 0:
                data['avg_time'] = data['total_time'] / data['count']

        # Add metrics from other components
        if hasattr(self.oanda_client, 'get_performance_metrics'):
            metrics['oanda_metrics'] = self.oanda_client.get_performance_metrics()

        if hasattr(self.firebase_client, 'get_performance_metrics'):
            metrics['firebase_metrics'] = self.firebase_client.get_performance_metrics()

        return metrics

    def update(self) -> Dict[str, Any]:
        """
        Update the trading engine state and execute trades if necessary.

        Returns:
            Dict[str, Any]: Update status and results
        """
        import time
        start_time = time.time()
        try:
            # Increment update cycle counter
            self.metrics['update_cycles'] += 1
            self.logger.log_info("Starting update cycle...")

            # Update risk management metrics periodically
            # This ensures runtime progress and other metrics are updated even without trades
            self._update_risk_management_metrics()

            # Update health metrics
            if self.health_client:
                # Update OANDA dependency status
                try:
                    # Check if we can get account data using retry mechanism
                    balance = self._get_account_balance()
                    if balance > 0:
                        self.health_client.update_dependency("oanda", "healthy", "Connected")
                    else:
                        self.health_client.update_dependency("oanda", "unhealthy", "Failed to get valid account balance")
                except Exception as e:
                    self.health_client.update_dependency("oanda", "unhealthy", str(e))

                # Update market data dependency status
                try:
                    # Check if market data is available
                    market_open = self.market_data_provider.check_market_status()
                    self.health_client.update_dependency("market_data", "healthy", f"Market {'open' if market_open else 'closed'}")
                except Exception as e:
                    self.health_client.update_dependency("market_data", "unhealthy", str(e))

            # Check if runtime has expired
            if self._check_runtime_expiration():
                return {
                    "status": "runtime_expired",
                    "message": f"Bot runtime of {self.runtime_days} days has expired."
                }

            # Check if we have trading sessions defined
            trading_sessions = None
            if hasattr(self.strategy, 'trading_session') and self.strategy.trading_session:
                trading_sessions = self.strategy.trading_session
                self.logger.log_info(f"Trading sessions: {trading_sessions}")

            # Check comprehensive market conditions
            market_conditions = self.market_data_provider.check_market_conditions(
                symbol=self.strategy.instrument,
                trading_sessions=trading_sessions,
                avoid_high_spread=self.avoid_high_spread
            )
            self.logger.log_info(f"Market conditions: {market_conditions['is_safe_to_trade']}")

            # Update market status in Firebase
            self.firebase_client.update_market_status(market_conditions)

            if not market_conditions["market_hours"]["is_open"]:
                self.logger.log_info(f"Market is closed: {market_conditions['reason']}")
                self.firebase_client.append_user_log(
                    f"🔴 Market is closed: {market_conditions['reason']}"
                )
                return {
                    "status": "market_closed",
                    "message": market_conditions['reason']
                }

            # Check if we're in a trading session
            if trading_sessions and 'session_info' in market_conditions and market_conditions['session_info']:
                session_info = market_conditions['session_info']
                if not session_info["in_session"]:
                    self.logger.log_info(f"Not in trading session: {', '.join(trading_sessions)}")
                    self.firebase_client.append_user_log(
                        f"🔵 Outside trading session: {', '.join(trading_sessions)}"
                    )
                    return {
                        "status": "not_in_session",
                        "message": f"Outside trading session: {', '.join(trading_sessions)}",
                        "session_info": session_info
                    }

            # Check if it's safe to trade based on other conditions
            if not market_conditions["is_safe_to_trade"]:
                self.logger.log_info(f"Market conditions not suitable for trading: {market_conditions['reason']}")
                self.firebase_client.append_user_log(
                    f"⚠️ Market conditions not suitable for trading: {market_conditions['reason']}"
                )
                return {
                    "status": "market_conditions_unsuitable",
                    "message": market_conditions['reason'],
                    "conditions": market_conditions
                }

            # Fetch market data
            timeframe = self.strategy.timeframe
            self.logger.log_info(f"Using timeframe: {timeframe}")

            market_data = self.market_data_provider.get_candles(
                symbol=self.strategy.instrument,
                timespan=timeframe,
                count=MIN_CANDLE_COUNT
            )

            # Check for stale data
            stale_data_result = self._check_stale_data(market_data, timeframe)
            self.logger.log_info(f"🔍 Stale data check: {stale_data_result['message']} (age: {stale_data_result.get('age_minutes', 'N/A'):.1f}min, threshold: {stale_data_result.get('threshold_minutes', 'N/A')}min)")

            if stale_data_result["is_stale"]:
                self.logger.log_warning(f"⚠️ Stale data detected: {stale_data_result['message']}")
                self.firebase_client.append_user_log(
                    f"⏸️ Trading paused: {stale_data_result['message']}"
                )
                return {
                    "status": "data_stale",
                    "message": stale_data_result['message'],
                    "details": stale_data_result
                }

            if market_data["status"] == "market_data_error":
                error_msg = f"Market data error: {market_data['message']}"
                self.logger.log_error(Exception(error_msg), error_msg)
                return {
                    "status": "market_data_error",
                    "message": error_msg
                }

            self.logger.log_info(f"Fetched {len(market_data['candles'])} candles")

            # Calculate indicators
            indicators = self.strategy.calculate_indicators(market_data["candles"])
            self.logger.log_info(f"Calculated indicators: {list(indicators.keys())}")

            # Get account balance using retry mechanism
            account_balance = self._get_account_balance()
            if account_balance <= 0:
                error_msg = "Failed to fetch valid account balance after retries"
                self.logger.log_error(Exception(error_msg), error_msg)
                return {
                    "status": "error",
                    "message": error_msg
                }

            # Create account summary for compatibility with existing code
            account_summary = {"balance": str(account_balance)}

            # Check daily loss limit
            if self._check_daily_loss_limit(account_balance):
                return {
                    "status": "daily_loss_limit_exceeded",
                    "message": f"Daily loss limit of {self.daily_loss_limit}% exceeded"
                }

            # Check total profit target
            if self._check_total_profit_target(account_balance):
                return {
                    "status": "total_profit_target_reached",
                    "message": f"Total profit target of {self.total_profit_target}% reached"
                }

            # Check total loss limit
            if self._check_total_loss_limit(account_balance):
                return {
                    "status": "total_loss_limit_exceeded",
                    "message": f"Total loss limit of {self.total_loss_limit}% exceeded"
                }

            # Get open trades and filter by current instrument
            all_open_trades = self.oanda_client.get_open_trades()
            open_trades = self._filter_trades_by_instrument(all_open_trades)
            self.logger.log_info(f"Open trades for {self.strategy.instrument}: {open_trades}")

            # Detect and update any trades that were automatically closed by SL/TP
            # Pass all trades for closure detection, but only process ones for our instrument
            self.detect_and_update_closed_trades(all_open_trades)

            # Check margin requirements early - stop bot if insufficient margin regardless of signals
            current_price = market_data["candles"][-1]["close"] if market_data["candles"] else 0
            if current_price > 0:
                # Calculate what position size would be if we were to trade
                position_size = self.strategy.get_position_size(account_balance, current_price)
                margin_check_result = self._check_margin_requirements(position_size, current_price, account_balance)

                if not margin_check_result["sufficient"]:
                    # Stop the bot due to insufficient margin
                    self.logger.log_error(None, f"🚨 STOPPING BOT - INSUFFICIENT MARGIN:")
                    self.logger.log_error(None, f"  💰 Required Margin: ${margin_check_result['required_margin']:.2f}")
                    self.logger.log_error(None, f"  📊 Available Margin: ${margin_check_result['available_margin']:.2f}")
                    self.logger.log_error(None, f"  ❌ Shortfall: ${margin_check_result['shortfall']:.2f}")
                    self.logger.log_error(None, f"  🛑 Bot cannot continue trading with current account balance")

                    # Send user-friendly notification
                    user_message = (
                        f"🛑 BOT STOPPED - INSUFFICIENT MARGIN\n"
                        f"📊 Required Margin: ${margin_check_result['required_margin']:.2f}\n"
                        f"💰 Available Margin: ${margin_check_result['available_margin']:.2f}\n"
                        f"❌ Shortfall: ${margin_check_result['shortfall']:.2f}\n\n"
                        f"🛠️ SOLUTIONS:\n"
                        f"• Add more funds to your account\n"
                        f"• Reduce risk percentage (currently {self.risk_percentage_pct}%)\n"
                        f"• Use tighter stop losses\n\n"
                        f"🤖 Please restart the bot after addressing the margin issue."
                    )

                    self.firebase_client.append_user_log(user_message)

                    # Send margin shortfall info to frontend
                    self._send_margin_shortfall_to_frontend(margin_check_result, position_size, current_price)

                    # Create thinking data for margin error display
                    margin_thinking_data = self._create_margin_error_thinking_data(
                        current_price, position_size, margin_check_result, account_balance
                    )

                    # Update thinking data in Firebase for UI display
                    if hasattr(self, 'firebase_client'):
                        self.firebase_client.update_thinking_data(margin_thinking_data)

                    # Return error status to stop the bot
                    return {
                        "status": "insufficient_margin",
                        "message": "Bot stopped due to insufficient margin",
                        "margin_details": margin_check_result
                    }

            # Update strategy with open trades information
            strategy_update = self.strategy.update(
                market_data={
                    "candles": market_data["candles"],
                    "indicators": indicators
                },
                account_balance=account_balance,
                open_trades=open_trades  # Pass open trades to the strategy
            )

            # Calculate trading signals
            signals = strategy_update.get("signals", {})
            self.logger.log_info(f"Generated signals: {signals}")

            # Update thinking data in Firebase
            thinking_data = strategy_update.get("thinking")
            if thinking_data:
                self.firebase_client.update_thinking_data(thinking_data)

            # Execute trades based on signals
            trade_result = None
            if signals:
                self.logger.log_info("Executing trades based on signals...")
                # Capture current thinking data for entry conditions storage
                current_thinking_data = strategy_update.get("thinking")
                trade_result = self._execute_trade(signals, {"candles": market_data["candles"], "indicators": indicators}, account_summary, open_trades, current_thinking_data)
                if trade_result:
                    self.logger.log_info(f"Trade executed: {trade_result}")
                else:
                    self.logger.log_info("Trade execution completed but no trade was placed")
            else:
                self.logger.log_info("No entry conditions met - continuing to monitor market")

            # Format candles for chart display
            formatted_candles = [{
                "time": candle["time"],
                "open": float(candle["open"]),
                "high": float(candle["high"]),
                "low": float(candle["low"]),
                "close": float(candle["close"]),
                "volume": int(candle["volume"])
            } for candle in market_data["candles"]]

            # Format indicators for chart display
            formatted_indicators = {}
            for name, values in indicators.items():
                formatted_indicators[name] = [{
                    "time": candle["time"],
                    "value": float(value) if value is not None else None
                } for candle, value in zip(market_data["candles"], values)]

            self.logger.log_info("Update cycle completed successfully")

            # Track execution time
            self._track_execution_time('update', start_time)

            return {
                "status": "success",
                "message": "Update completed successfully",
                "market_data": {
                    "candles": formatted_candles,
                    "indicators": formatted_indicators
                },
                "account_summary": account_summary,
                "open_trades": open_trades,
                "trade": trade_result,
                "performance_metrics": self.get_performance_metrics() if self.metrics['update_cycles'] % 10 == 0 else None  # Include metrics every 10 cycles
            }

        except SystemExit as e:
            # Re-raise SystemExit to propagate it up to the main loop
            self.logger.log_info(f"SystemExit received in trading engine update: {str(e)}")
            raise
        except Exception as e:
            error_msg = f"Error in update cycle: {str(e)}"
            self.logger.log_error(e, error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    def _execute_trade(self, signals: Dict[str, Any], market_data: Dict[str, Any], account_summary: Dict[str, Any], open_trades: List[TradeHistoryRow], thinking_data: Dict[str, Any] = None) -> Optional[TradeExecutionResponse]:
        """
        Execute a trade based on strategy signals.

        Args:
            signals (Dict[str, Any]): Trading signals from strategy
            thinking_data (Dict[str, Any]): Current thinking data for storing entry conditions
            market_data (Dict[str, Any]): Current market data
            account_summary (Dict[str, Any]): Account summary from OANDA
            open_trades (List[TradeHistoryRow]): List of open trades

        Returns:
            Optional[TradeExecutionResponse]: Trade execution results if successful
        """
        import time
        start_time = time.time()

        # Store thinking data for entry conditions
        self.current_thinking_data = thinking_data

        try:
            # Filter open trades to only include those for the current instrument
            filtered_open_trades = self._filter_trades_by_instrument(open_trades)

            # If we have a CLOSE signal and open trades for this instrument, process it first
            if signals["action"] == "CLOSE" and len(filtered_open_trades) > 0:
                return self._process_close_signal(signals, market_data, account_summary, filtered_open_trades)

            # For BUY/SELL signals, check if we have open trades for this instrument
            if signals["action"] in ["BUY", "SELL"] and len(filtered_open_trades) > 0:
                self.logger.log_info(f"Open trades for {self.strategy.instrument}: {filtered_open_trades} already exists - skipping trade")
                self.firebase_client.append_user_log(
                    f"⚠️ Trade conditions met but we currently have an open position for {self.strategy.instrument}. "
                    "Forex trading rules require closing existing positions before opening new ones in the same instrument. "
                    "The bot will wait for the existing position to be closed before executing the new trade."
                )
                return None

            # Get current price
            current_price = float(market_data["candles"][-1]["close"])

            # Pip value will be calculated in the strategy's get_position_size method
            # based on the instrument and current price

            # Calculate position size based on account balance and risk parameters
            account_balance = float(account_summary["balance"])

            # Use the strategy's get_position_size method to calculate position size
            # This will handle all three scenarios: Fixed Pips, Indicator-Based SL, and Risk-Based SL
            # The pip value will be calculated inside the method based on the instrument and current price
            position_size = self.strategy.get_position_size(account_balance, current_price)

            # No max position size limit - use risk percentage as primary constraint
            # Position size is determined purely by: Risk Amount / (Stop Loss Distance × Pip Value)
            position_value_dollars = position_size * current_price

            # Enhanced logging for position size calculation
            risk_amount = account_balance * (self.risk_percentage_pct / 100)
            self.logger.log_info(f"📊 POSITION SIZE CALCULATION:")
            self.logger.log_info(f"  💰 Account Balance: ${account_balance:.2f}")
            self.logger.log_info(f"  🎯 Risk Percentage: {self.risk_percentage_pct}%")
            self.logger.log_info(f"  💵 Risk Amount: ${risk_amount:.2f}")
            self.logger.log_info(f"  📈 Current Price: {current_price:.5f}")
            self.logger.log_info(f"  📏 Calculated Position: {position_size:.0f} units")
            self.logger.log_info(f"  💲 Position Value: ${position_value_dollars:.2f}")
            self.logger.log_info(f"  📊 Position as % of Balance: {(position_value_dollars/account_balance)*100:.1f}%")

            # Margin check is now done earlier in the update cycle, so we can proceed with trade execution

            # Calculate stop loss and take profit prices based on the stop loss method
            pip_size = 0.01 if 'JPY' in self.strategy.instrument.upper() else 0.0001
            risk_reward_ratio = float(self.strategy.risk_reward_ratio) if hasattr(self.strategy, 'risk_reward_ratio') and self.strategy.risk_reward_ratio else 2.0

            if hasattr(self.strategy, 'stop_loss_method') and self.strategy.stop_loss_method == "fixed":
                # For fixed pips, calculate actual prices based on pip values
                sl_pips = float(self.strategy.fixed_pips) if hasattr(self.strategy, 'fixed_pips') and self.strategy.fixed_pips else 20
                tp_pips = sl_pips * risk_reward_ratio
                self.logger.log_info(f"Using position size: {position_size}, stop loss: {sl_pips} pips, take profit: {tp_pips} pips")

            elif hasattr(self.strategy, 'stop_loss_method') and self.strategy.stop_loss_method == "indicator":
                # For indicator-based stop loss, we'll calculate actual prices based on indicator values
                self.logger.log_info(f"Using indicator-based stop loss method")
                # sl_pips and tp_pips will be calculated dynamically based on indicator values during trade execution

            else:
                # Use percentage-based calculations for other methods
                stop_loss_pct = self.stop_loss_pct / 100.0
                take_profit_pct = self.take_profit_pct / 100.0
                self.logger.log_info(f"Using position size: {position_size}, stop loss: {self.stop_loss_pct}%, take profit: {self.take_profit_pct}%")

            # Execute trade based on signal (use filtered trades)
            if signals["action"] == "BUY" and len(filtered_open_trades) == 0:
                # Calculate stop loss and take profit for long position
                if hasattr(self.strategy, 'stop_loss_method') and self.strategy.stop_loss_method == "fixed":
                    # Fixed pips calculation
                    stop_loss_price = current_price - (sl_pips * pip_size)
                    take_profit_price = current_price + (tp_pips * pip_size)
                elif hasattr(self.strategy, 'stop_loss_method') and self.strategy.stop_loss_method == "indicator":
                    # Indicator-based calculation
                    self.logger.log_info(f"Calculating indicator-based levels for LONG trade at price {current_price}")
                    stop_loss_price, take_profit_price = self._calculate_indicator_based_levels(
                        current_price, True, market_data, risk_reward_ratio
                    )
                    self.logger.log_info(f"Calculated LONG levels - Entry: {current_price}, SL: {stop_loss_price}, TP: {take_profit_price}")
                else:
                    # Percentage calculation
                    stop_loss_price = current_price * (1 - stop_loss_pct)
                    take_profit_price = current_price * (1 + take_profit_pct)

                trade_result = self.oanda_client.execute_trade(
                    instrument=self.strategy.instrument,
                    units=position_size,
                    type=TradeType.LONG,
                    price=current_price,
                    stop_loss=stop_loss_price,
                    take_profit=take_profit_price
                )

                if trade_result:
                    # Increment trades executed counter
                    self.metrics['trades_executed'] += 1

                self.logger.log_info(f"trade_result: {trade_result}")

                if trade_result:
                    self.firebase_client.append_user_log(
                        "🟢 Trade executed: LONG order placed"
                    )

                    # Report trade execution metrics
                    if self.health_client:
                        self.health_client.update_dependency("trade_execution", "healthy", "Successfully executed LONG trade")

                    return trade_result

            elif signals["action"] == "SELL" and len(filtered_open_trades) == 0:
                # Calculate stop loss and take profit for short position
                if hasattr(self.strategy, 'stop_loss_method') and self.strategy.stop_loss_method == "fixed":
                    # Fixed pips calculation
                    stop_loss_price = current_price + (sl_pips * pip_size)
                    take_profit_price = current_price - (tp_pips * pip_size)
                elif hasattr(self.strategy, 'stop_loss_method') and self.strategy.stop_loss_method == "indicator":
                    # Indicator-based calculation
                    self.logger.log_info(f"Calculating indicator-based levels for SHORT trade at price {current_price}")
                    stop_loss_price, take_profit_price = self._calculate_indicator_based_levels(
                        current_price, False, market_data, risk_reward_ratio
                    )
                    self.logger.log_info(f"Calculated SHORT levels - Entry: {current_price}, SL: {stop_loss_price}, TP: {take_profit_price}")
                else:
                    # Percentage calculation
                    stop_loss_price = current_price * (1 + stop_loss_pct)
                    take_profit_price = current_price * (1 - take_profit_pct)

                self.logger.log_info(f"Executing SHORT trade with position_size={position_size}, using abs({position_size})={abs(position_size)} units")
                trade_result = self.oanda_client.execute_trade(
                    instrument=self.strategy.instrument,
                    units=abs(position_size),  # Use absolute value, OANDA client will handle sign conversion for SHORT
                    type=TradeType.SHORT,
                    price=current_price,
                    stop_loss=stop_loss_price,
                    take_profit=take_profit_price
                )

                if trade_result:
                    # Increment trades executed counter
                    self.metrics['trades_executed'] += 1

                if trade_result:
                    self.firebase_client.append_user_log(
                        "🟢 Trade executed: SHORT order placed"
                    )

                    # Report trade execution metrics
                    if self.health_client:
                        self.health_client.update_dependency("trade_execution", "healthy", "Successfully executed SHORT trade")

                    return trade_result

            # CLOSE signal is now handled by _process_close_signal

            # Track execution time
            self._track_execution_time('_execute_trade', start_time)

            return None

        except Exception as e:
            self.logger.log_error(e, "Error executing trade")
            # Track execution time even for errors
            self._track_execution_time('_execute_trade_error', start_time)
            return None

    def get_trade_history(self) -> List[Dict[str, Any]]:
        """
        Get the history of executed trades.

        Returns:
            List[Dict[str, Any]]: List of trade history entries
        """
        return self.trade_history

    def get_current_position(self) -> Optional[Dict[str, Any]]:
        """
        Get the current trading position.

        Returns:
            Optional[Dict[str, Any]]: Current position information if exists
        """
        positions = self.oanda_client.get_open_positions()
        return positions[0] if positions else None

    def convert_trade_execution_response_to_trade_history_row(self, trade_execution_response: TradeExecutionResponse, entry_conditions: dict = None) -> TradeHistoryRow:
        """
        Convert a TradeExecutionResponse to a TradeHistoryRow.

        Args:
            trade_execution_response (TradeExecutionResponse): The trade execution response to convert
            entry_conditions (dict): Entry conditions that triggered this trade
        """
        return TradeHistoryRow(
            tradeID=trade_execution_response.tradeID,
            type=trade_execution_response.type,
            status=TradeStatus.OPEN,
            instrument=trade_execution_response.instrument,
            units=float(trade_execution_response.units),
            price=float(trade_execution_response.price),
            openTime=trade_execution_response.time if isinstance(trade_execution_response.time, datetime) else datetime.fromisoformat(trade_execution_response.time.replace('Z', '+00:00')),
            initialMarginRequired=float(trade_execution_response.initialMarginRequired),
            halfSpreadCost=float(trade_execution_response.halfSpreadCost),
            commission=float(trade_execution_response.commission),
            unrealizedPL=float(trade_execution_response.pl),
            entryConditions=entry_conditions
        )

    def detect_and_update_closed_trades(self, current_open_trades: List[TradeHistoryRow]) -> None:
        """
        Detect trades that were automatically closed by SL/TP and update Firebase.

        Args:
            current_open_trades: Current list of open trades from OANDA
        """
        try:
            # If this is the first check, just store the current trades
            if not self.previous_open_trades:
                self.previous_open_trades = current_open_trades.copy()
                return

            # Find trades that were open before but are not open now (i.e., closed)
            previous_trade_ids = {trade.tradeID for trade in self.previous_open_trades}
            current_trade_ids = {trade.tradeID for trade in current_open_trades}
            closed_trade_ids = previous_trade_ids - current_trade_ids

            if closed_trade_ids:
                self.logger.log_info(f"Detected {len(closed_trade_ids)} automatically closed trades: {closed_trade_ids}")

                # For each closed trade, get the details and update Firebase
                for trade_id in closed_trade_ids:
                    try:
                        # Check if this trade was already processed by the fast loop
                        if hasattr(self, 'fast_update_loop') and self.fast_update_loop and hasattr(self.fast_update_loop, 'was_trade_processed_by_fast_loop'):
                            if self.fast_update_loop.was_trade_processed_by_fast_loop(trade_id):
                                self.logger.log_info(f"Trade {trade_id} already processed by fast loop, skipping main loop processing")
                                continue

                        self.logger.log_info(f"Processing automatically closed trade: {trade_id}")

                        # Get the original trade details from our previous list
                        original_trade = next((t for t in self.previous_open_trades if t.tradeID == trade_id), None)
                        if not original_trade:
                            self.logger.log_warning(f"Could not find original trade details for {trade_id}")
                            continue

                        # Get closed trade details from OANDA, passing the trade open time to help narrow the search
                        trade_open_time = original_trade.openTime if original_trade else None
                        closed_trade_details = self._get_closed_trade_details(trade_id, trade_open_time)

                        if closed_trade_details:
                            # Create updated trade record with closed status
                            closed_trade = TradeHistoryRow(
                                tradeID=trade_id,
                                instrument=original_trade.instrument,
                                type=original_trade.type,
                                units=original_trade.units,
                                price=original_trade.price,
                                time=original_trade.time,
                                status=TradeStatus.CLOSED,
                                initialMarginRequired=original_trade.initialMarginRequired,
                                halfSpreadCost=original_trade.halfSpreadCost,
                                commission=original_trade.commission,
                                realizedPL=closed_trade_details.get('realizedPL', 0.0),
                                unrealizedPL=0.0,  # No unrealized P&L for closed trades
                                closeTime=closed_trade_details.get('closeTime'),
                                takeProfitPrice=original_trade.takeProfitPrice,
                                stopLossPrice=original_trade.stopLossPrice
                            )

                            # Update Firebase with the closed trade
                            self.firebase_client.update_open_trades([closed_trade])

                            # CRITICAL: Update risk management metrics with the realized P&L
                            realized_pl = closed_trade_details.get('realizedPL', 0.0)
                            if realized_pl != 0:
                                self.logger.log_info(f"Updating risk management metrics for automatically closed trade {trade_id} with P&L: {realized_pl}")

                                # Update daily loss tracker if negative P&L
                                if realized_pl < 0:
                                    self._update_daily_loss(realized_pl, update_metrics=False)

                                # Update total profit and loss trackers
                                self._update_total_pnl(realized_pl, update_metrics=False)

                                # Update risk management metrics in Firebase
                                self._update_risk_management_metrics()

                                self.logger.log_info(f"Risk management metrics updated: totalProfit={self.total_profit:.2f}, totalLoss={self.total_loss:.2f}, dailyLoss={self.daily_loss:.2f}")

                            # Log the closure
                            closure_reason = self._determine_closure_reason(closed_trade_details, original_trade)
                            self.logger.log_info(f"Trade {trade_id} was automatically closed: {closure_reason}")
                            self.firebase_client.append_user_log(
                                f"🔴 Trade {trade_id} automatically closed: {closure_reason} | P&L: ${realized_pl:.2f}"
                            )

                            # Update metrics
                            self.metrics['trades_closed'] += 1

                        else:
                            self.logger.log_warning(f"Could not get closed trade details for {trade_id}")

                    except Exception as e:
                        self.logger.log_error(e, f"Error processing closed trade {trade_id}")
                        continue

            # Update the previous trades list for next comparison
            self.previous_open_trades = current_open_trades.copy()

        except Exception as e:
            self.logger.log_error(e, "Error in detect_and_update_closed_trades")

    def _get_closed_trade_details(self, trade_id: str, trade_open_time: datetime = None) -> dict:
        """
        Get details of a closed trade from OANDA.

        Args:
            trade_id: The trade ID to get details for
            trade_open_time: When the trade was opened (optional, helps narrow search)

        Returns:
            dict: Closed trade details including realizedPL, closeTime, and closeReason
        """
        try:
            self.logger.log_info(f"Getting closed trade details for {trade_id}")
            closed_trade_details = self.oanda_client.get_closed_trade_details(trade_id, trade_open_time)

            if closed_trade_details:
                self.logger.log_info(f"Retrieved closed trade details for {trade_id}: realizedPL={closed_trade_details.get('realizedPL', 0.0)}, closeReason={closed_trade_details.get('closeReason', 'UNKNOWN')}")
                return closed_trade_details
            else:
                self.logger.log_warning(f"No closed trade details found for {trade_id}")
                return None

        except Exception as e:
            self.logger.log_error(e, f"Error getting closed trade details for {trade_id}")
            return None

    def _calculate_indicator_based_levels(self, current_price: float, is_long: bool,
                                        market_data: Dict, risk_reward_ratio: float) -> tuple[float, float]:
        """
        Calculate stop loss and take profit levels based on indicator values.

        Args:
            current_price: Current market price
            is_long: True for long trades, False for short trades
            market_data: Market data containing candles and indicators
            risk_reward_ratio: Risk to reward ratio for take profit calculation

        Returns:
            tuple: (stop_loss_price, take_profit_price)
        """
        try:
            if not hasattr(self.strategy, 'indicator_based_sl') or not self.strategy.indicator_based_sl:
                self.logger.log_warning("No indicator_based_sl configuration found, using default levels")
                # Fallback to percentage-based calculation
                stop_loss_pct = 0.02  # 2%
                take_profit_pct = stop_loss_pct * risk_reward_ratio
                if is_long:
                    return (current_price * (1 - stop_loss_pct), current_price * (1 + take_profit_pct))
                else:
                    return (current_price * (1 + stop_loss_pct), current_price * (1 - take_profit_pct))

            indicator_type = self.strategy.indicator_based_sl.get("indicator", "atr")
            self.logger.log_info(f"Calculating indicator-based levels for {indicator_type}, is_long={is_long}")
            self.logger.log_info(f"Available indicators in market data: {list(market_data.get('indicators', {}).keys())}")

            if indicator_type == "bollinger":
                return self._calculate_bollinger_levels(current_price, is_long, market_data, risk_reward_ratio)
            elif indicator_type == "atr":
                return self._calculate_atr_levels(current_price, is_long, market_data, risk_reward_ratio)
            elif indicator_type == "support_resistance":
                return self._calculate_sr_levels(current_price, is_long, market_data, risk_reward_ratio)
            else:
                self.logger.log_warning(f"Unknown indicator type: {indicator_type}, using default levels")
                # Fallback to percentage-based calculation
                stop_loss_pct = 0.02  # 2%
                take_profit_pct = stop_loss_pct * risk_reward_ratio
                if is_long:
                    return (current_price * (1 - stop_loss_pct), current_price * (1 + take_profit_pct))
                else:
                    return (current_price * (1 + stop_loss_pct), current_price * (1 - take_profit_pct))

        except Exception as e:
            self.logger.log_error(f"Error calculating indicator-based levels: {e}")
            # Fallback to percentage-based calculation
            stop_loss_pct = 0.02  # 2%
            take_profit_pct = stop_loss_pct * risk_reward_ratio
            if is_long:
                return (current_price * (1 - stop_loss_pct), current_price * (1 + take_profit_pct))
            else:
                return (current_price * (1 + stop_loss_pct), current_price * (1 - take_profit_pct))

    def _calculate_bollinger_levels(self, current_price: float, is_long: bool,
                                  market_data: Dict, risk_reward_ratio: float) -> tuple[float, float]:
        """
        Calculate stop loss and take profit levels based on Bollinger Bands.

        For LONG trades: Stop loss at lower band, take profit calculated based on RRR
        For SHORT trades: Stop loss at upper band, take profit calculated based on RRR
        """
        try:
            # Get Bollinger Bands indicator data from market data
            indicators = market_data.get("indicators", {})

            # Find Bollinger Bands indicator components (stored as separate indicators)
            # Look for keys ending with _upper, _lower, _middle from the same indicator
            upper_key = None
            lower_key = None
            middle_key = None

            # First, try to find any Bollinger Bands components by looking for _upper, _lower, _middle patterns
            # that belong to the same indicator ID
            indicator_groups = {}

            for key in indicators.keys():
                if "_upper" in key.lower():
                    base_id = key.replace("_upper", "").replace("_Upper", "")
                    if base_id not in indicator_groups:
                        indicator_groups[base_id] = {}
                    indicator_groups[base_id]["upper"] = key
                elif "_lower" in key.lower():
                    base_id = key.replace("_lower", "").replace("_Lower", "")
                    if base_id not in indicator_groups:
                        indicator_groups[base_id] = {}
                    indicator_groups[base_id]["lower"] = key
                elif "_middle" in key.lower():
                    base_id = key.replace("_middle", "").replace("_Middle", "")
                    if base_id not in indicator_groups:
                        indicator_groups[base_id] = {}
                    indicator_groups[base_id]["middle"] = key

            # Find a group that has both upper and lower (Bollinger Bands)
            for base_id, components in indicator_groups.items():
                if "upper" in components and "lower" in components:
                    upper_key = components["upper"]
                    lower_key = components["lower"]
                    middle_key = components.get("middle")
                    self.logger.log_info(f"Found Bollinger Bands components: upper={upper_key}, lower={lower_key}, middle={middle_key}")
                    break

            if not upper_key or not lower_key:
                self.logger.log_warning(f"Bollinger Bands components not found. Available indicators: {list(indicators.keys())}")
                self.logger.log_warning(f"Indicator groups found: {indicator_groups}")
                raise ValueError("Bollinger Bands indicator components not found")

            upper_data = indicators[upper_key]
            lower_data = indicators[lower_key]
            middle_data = indicators.get(middle_key, []) if middle_key else []

            if not upper_data or not lower_data or len(upper_data) == 0 or len(lower_data) == 0:
                self.logger.log_warning("Empty Bollinger Bands data")
                raise ValueError("Empty Bollinger Bands data")

            # Get the latest Bollinger Bands values
            upper_band = upper_data[-1]  # Most recent upper band value
            lower_band = lower_data[-1]  # Most recent lower band value
            middle_band = middle_data[-1] if middle_data else None  # Most recent middle band value

            if upper_band is None or lower_band is None:
                self.logger.log_warning("Bollinger Bands values are None")
                raise ValueError("Bollinger Bands values are None")

            self.logger.log_info(f"Bollinger Bands - Upper: {upper_band}, Middle: {middle_band}, Lower: {lower_band}, Current: {current_price}")

            # Calculate stop loss and take profit
            if is_long:
                # For long trades: stop loss at lower band
                stop_loss_price = float(lower_band)
                # Calculate take profit based on risk-reward ratio
                sl_distance = abs(current_price - stop_loss_price)
                take_profit_price = current_price + (sl_distance * risk_reward_ratio)
            else:
                # For short trades: stop loss at upper band
                stop_loss_price = float(upper_band)
                # Calculate take profit based on risk-reward ratio
                sl_distance = abs(stop_loss_price - current_price)
                take_profit_price = current_price - (sl_distance * risk_reward_ratio)

            self.logger.log_info(f"Bollinger Bands levels - SL: {stop_loss_price}, TP: {take_profit_price}, RRR: {risk_reward_ratio}")
            return (stop_loss_price, take_profit_price)

        except Exception as e:
            self.logger.log_error(f"Error calculating Bollinger Bands levels: {e}")
            raise

    def _calculate_atr_levels(self, current_price: float, is_long: bool,
                            market_data: Dict, risk_reward_ratio: float) -> tuple[float, float]:
        """
        Calculate stop loss and take profit levels based on ATR.
        """
        try:
            # Get ATR parameters
            atr_value = self.strategy.indicator_based_sl.get("value", 0.001)
            sl_multiplier = self.strategy.indicator_based_sl.get("multiplier", 1.5)

            # Calculate stop loss distance
            sl_distance = atr_value * sl_multiplier

            if is_long:
                stop_loss_price = current_price - sl_distance
                take_profit_price = current_price + (sl_distance * risk_reward_ratio)
            else:
                stop_loss_price = current_price + sl_distance
                take_profit_price = current_price - (sl_distance * risk_reward_ratio)

            self.logger.log_info(f"ATR levels - SL: {stop_loss_price}, TP: {take_profit_price}, ATR: {atr_value}, Multiplier: {sl_multiplier}")
            return (stop_loss_price, take_profit_price)

        except Exception as e:
            self.logger.log_error(f"Error calculating ATR levels: {e}")
            raise

    def _calculate_sr_levels(self, current_price: float, is_long: bool,
                           market_data: Dict, risk_reward_ratio: float) -> tuple[float, float]:
        """
        Calculate stop loss and take profit levels based on Support & Resistance.
        """
        try:
            pip_size = 0.01 if 'JPY' in self.strategy.instrument.upper() else 0.0001

            # Get S&R parameters from strategy configuration
            sr_params = self.strategy.indicator_based_sl.get("parameters", {})
            left_bars = sr_params.get("left", 10)
            right_bars = sr_params.get("right", 10)

            self.logger.log_info(f"Calculating S&R levels with left={left_bars}, right={right_bars} bars")

            # Try to find actual S&R levels from indicators
            indicators = market_data.get("indicators", {})
            support_levels = None
            resistance_levels = None

            # Look for S&R indicator data with semantic keys
            for key, values in indicators.items():
                if "_support" in key and values:
                    support_levels = values
                    self.logger.log_info(f"Found support levels from key: {key}, count: {len(values)}")
                elif "_resistance" in key and values:
                    resistance_levels = values
                    self.logger.log_info(f"Found resistance levels from key: {key}, count: {len(values)}")

            # Calculate stop loss and take profit based on actual S&R levels
            if is_long:
                # For long trades: SL at nearest support below, TP at nearest resistance above
                stop_loss_price = self._find_nearest_support(current_price, support_levels)
                take_profit_price = self._find_nearest_resistance(current_price, resistance_levels)

                # If no suitable levels found, use distance-based calculation
                if stop_loss_price is None or stop_loss_price >= current_price:
                    sl_distance = 15.0 * pip_size  # Default 15 pips
                    stop_loss_price = current_price - sl_distance
                    self.logger.log_info(f"No suitable support found, using default SL distance: {sl_distance}")

                if take_profit_price is None or take_profit_price <= current_price:
                    sl_distance = abs(current_price - stop_loss_price)
                    take_profit_price = current_price + (sl_distance * risk_reward_ratio)
                    self.logger.log_info(f"No suitable resistance found, using risk-reward based TP")

            else:
                # For short trades: SL at nearest resistance above, TP at nearest support below
                stop_loss_price = self._find_nearest_resistance(current_price, resistance_levels)
                take_profit_price = self._find_nearest_support(current_price, support_levels)

                # If no suitable levels found, use distance-based calculation
                if stop_loss_price is None or stop_loss_price <= current_price:
                    sl_distance = 15.0 * pip_size  # Default 15 pips
                    stop_loss_price = current_price + sl_distance
                    self.logger.log_info(f"No suitable resistance found, using default SL distance: {sl_distance}")

                if take_profit_price is None or take_profit_price >= current_price:
                    sl_distance = abs(stop_loss_price - current_price)
                    take_profit_price = current_price - (sl_distance * risk_reward_ratio)
                    self.logger.log_info(f"No suitable support found, using risk-reward based TP")

            self.logger.log_info(f"S&R levels calculated: Current={current_price:.5f}, SL={stop_loss_price:.5f}, TP={take_profit_price:.5f}")
            return (stop_loss_price, take_profit_price)

        except Exception as e:
            self.logger.log_error(f"Error calculating S&R levels: {e}")
            # Fallback to percentage-based calculation
            stop_loss_pct = 0.02  # 2%
            take_profit_pct = stop_loss_pct * risk_reward_ratio
            if is_long:
                return (current_price * (1 - stop_loss_pct), current_price * (1 + take_profit_pct))
            else:
                return (current_price * (1 + stop_loss_pct), current_price * (1 - take_profit_pct))

    def _find_nearest_support(self, current_price: float, support_levels: list) -> float:
        """
        Find the nearest support level below the current price.

        Args:
            current_price: Current market price
            support_levels: List of support level values

        Returns:
            float: Nearest support level below current price, or None if not found
        """
        if not support_levels:
            return None

        # Filter support levels that are below current price and not None
        valid_supports = [level for level in support_levels if level is not None and level < current_price]

        if not valid_supports:
            return None

        # Return the highest support level below current price (nearest)
        return max(valid_supports)

    def _find_nearest_resistance(self, current_price: float, resistance_levels: list) -> float:
        """
        Find the nearest resistance level above the current price.

        Args:
            current_price: Current market price
            resistance_levels: List of resistance level values

        Returns:
            float: Nearest resistance level above current price, or None if not found
        """
        if not resistance_levels:
            return None

        # Filter resistance levels that are above current price and not None
        valid_resistances = [level for level in resistance_levels if level is not None and level > current_price]

        if not valid_resistances:
            return None

        # Return the lowest resistance level above current price (nearest)
        return min(valid_resistances)

    def _check_margin_requirements(self, position_size: float, current_price: float, account_balance: float) -> Dict[str, Any]:
        """
        Check if there's sufficient margin for the proposed trade.

        Args:
            position_size: Position size in units
            current_price: Current market price
            account_balance: Current account balance

        Returns:
            Dict containing margin check results
        """
        try:
            # Calculate required margin (typically 2-5% of position value for major pairs)
            # OANDA uses different margin rates for different instruments
            # For major pairs like EUR/USD, margin requirement is typically 3.33% (30:1 leverage)
            position_value = position_size * current_price

            # Estimate margin requirement (this is conservative - actual may be lower)
            # Major pairs: 3.33% (30:1 leverage)
            # Minor pairs: 5% (20:1 leverage)
            # Exotic pairs: 10% (10:1 leverage)
            instrument = self.strategy.instrument.upper()
            if any(pair in instrument for pair in ['EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 'AUD/USD', 'USD/CAD', 'NZD/USD']):
                margin_rate = 0.0333  # 3.33% for major pairs
            elif any(pair in instrument for pair in ['EUR/GBP', 'EUR/JPY', 'GBP/JPY', 'CHF/JPY', 'EUR/CHF', 'AUD/JPY', 'GBP/CHF']):
                margin_rate = 0.05    # 5% for minor pairs
            else:
                margin_rate = 0.10    # 10% for exotic pairs

            required_margin = position_value * margin_rate

            # Get available margin (account balance minus used margin)
            # For simplicity, we'll use account balance as available margin
            # In reality, OANDA calculates this more precisely
            available_margin = account_balance

            sufficient = available_margin >= required_margin
            shortfall = max(0, required_margin - available_margin)

            return {
                "sufficient": sufficient,
                "required_margin": required_margin,
                "available_margin": available_margin,
                "shortfall": shortfall,
                "margin_rate": margin_rate,
                "position_value": position_value
            }

        except Exception as e:
            self.logger.log_error(f"Error checking margin requirements: {e}")
            # If we can't check margin, assume insufficient to be safe
            return {
                "sufficient": False,
                "required_margin": 0,
                "available_margin": 0,
                "shortfall": 0,
                "margin_rate": 0,
                "position_value": 0,
                "error": str(e)
            }

    def _send_margin_shortfall_to_frontend(self, margin_check: Dict[str, Any], position_size: float, current_price: float):
        """
        Send margin shortfall information to frontend via Firebase.

        Args:
            margin_check: Result from _check_margin_requirements
            position_size: Requested position size in units
            current_price: Current market price
        """
        try:
            shortfall_message = (
                f"⚠️ Trade skipped due to insufficient margin:\n"
                f"• Required position: {position_size:.0f} units (${margin_check['position_value']:.2f})\n"
                f"• Required margin: ${margin_check['required_margin']:.2f} ({margin_check['margin_rate']*100:.1f}% of position)\n"
                f"• Available margin: ${margin_check['available_margin']:.2f}\n"
                f"• Shortfall: ${margin_check['shortfall']:.2f}\n\n"
                f"💡 To fix this:\n"
                f"• Add ${margin_check['shortfall']:.2f} to your account, OR\n"
                f"• Reduce risk percentage from {self.risk_percentage_pct}% to {(margin_check['available_margin']/margin_check['required_margin']*self.risk_percentage_pct):.2f}%"
            )

            # Send to Firebase for frontend display
            if hasattr(self, 'firebase_client') and self.firebase_client:
                self.firebase_client.append_user_log(shortfall_message)

            self.logger.log_info("Margin shortfall information sent to frontend")

        except Exception as e:
            self.logger.log_error(f"Error sending margin shortfall to frontend: {e}")

    def _create_margin_error_thinking_data(self, current_price: float, position_size: int,
                                         margin_check_result: dict, account_balance: float) -> dict:
        """Create thinking data specifically for margin error display."""
        try:
            # Get strategy details for position sizing explanation
            stop_loss_pips = self.strategy._calculate_stop_loss_distance(current_price)
            take_profit_pips = self.strategy._calculate_take_profit_distance()
            pip_value = self.strategy._calculate_pip_value(current_price, position_size)

            # Calculate risk details
            stop_loss_dollars = stop_loss_pips * pip_value
            risk_percentage = (stop_loss_dollars / account_balance) * 100 if account_balance > 0 else 0

            return {
                "timestamp": datetime.utcnow().isoformat(),
                "current_price": current_price,
                "bot_status": "insufficient_margin",
                "margin_error": True,
                "margin_details": {
                    "required_margin": margin_check_result.get("required_margin", 0),
                    "available_margin": margin_check_result.get("available_margin", 0),
                    "shortfall": margin_check_result.get("shortfall", 0),
                    "account_balance": account_balance,
                    "position_size": position_size,
                    "stop_loss_pips": stop_loss_pips,
                    "take_profit_pips": take_profit_pips,
                    "stop_loss_dollars": stop_loss_dollars,
                    "risk_percentage": risk_percentage,
                    "pip_value": pip_value,
                    "leverage": margin_check_result.get("leverage", 50),
                    "margin_rate": margin_check_result.get("margin_rate", 0.02)
                },
                "explanation": {
                    "title": "Bot Stopped - Insufficient Margin",
                    "summary": f"The bot calculated it would need ${margin_check_result.get('required_margin', 0):,.2f} in margin to execute trades, but only ${margin_check_result.get('available_margin', 0):,.2f} is available.",
                    "calculation_steps": [
                        f"1. Account Balance: ${account_balance:,.2f}",
                        f"2. Risk per Trade: {self.risk_percentage_pct}% = ${stop_loss_dollars:,.2f}",
                        f"3. Stop Loss Distance: {stop_loss_pips:.1f} pips",
                        f"4. Position Size Needed: {position_size:,} units",
                        f"5. Margin Required: ${margin_check_result.get('required_margin', 0):,.2f}",
                        f"6. Margin Available: ${margin_check_result.get('available_margin', 0):,.2f}",
                        f"7. Shortfall: ${margin_check_result.get('shortfall', 0):,.2f}"
                    ],
                    "solutions": [
                        f"• Add ${margin_check_result.get('shortfall', 0):,.2f} more funds to your account",
                        f"• Reduce risk percentage from {self.risk_percentage_pct}% to {max(0.1, (margin_check_result.get('available_margin', 0) / margin_check_result.get('required_margin', 1)) * self.risk_percentage_pct):.1f}%",
                        "• Use tighter stop losses to reduce position size",
                        "• Choose strategies with smaller position requirements"
                    ]
                }
            }

        except Exception as e:
            self.logger.log_error(e, "Error creating margin error thinking data")
            return {
                "timestamp": datetime.utcnow().isoformat(),
                "bot_status": "insufficient_margin",
                "margin_error": True,
                "explanation": {
                    "title": "Bot Stopped - Insufficient Margin",
                    "summary": "The bot stopped due to insufficient margin for trading."
                }
            }

    def _determine_closure_reason(self, closed_trade_details: dict, original_trade: TradeHistoryRow) -> str:
        """
        Determine why a trade was closed (SL, TP, or other reason).

        Args:
            closed_trade_details: Details from OANDA about the closed trade
            original_trade: Original trade information

        Returns:
            str: Human-readable closure reason
        """
        try:
            # This logic would need to be enhanced based on OANDA's response format
            close_reason = closed_trade_details.get('closeReason', 'UNKNOWN')
            realized_pl = closed_trade_details.get('realizedPL', 0.0)

            if close_reason == 'STOP_LOSS_ORDER':
                return f"Stop Loss hit (${realized_pl:.2f})"
            elif close_reason == 'TAKE_PROFIT_ORDER':
                return f"Take Profit hit (${realized_pl:.2f})"
            elif realized_pl > 0:
                return f"Closed with profit (${realized_pl:.2f})"
            elif realized_pl < 0:
                return f"Closed with loss (${realized_pl:.2f})"
            else:
                return f"Automatically closed (${realized_pl:.2f})"

        except Exception as e:
            self.logger.log_error(e, "Error determining closure reason")
            return "Automatically closed"

    def close_and_update_open_trades(self, is_shutdown: bool = False) -> bool:
        """
        Close all open trades and update the trade history.

        Args:
            is_shutdown (bool): Whether this is being called during shutdown

        Returns:
            bool: True if all trades were closed successfully, False otherwise
        """
        try:
            self.logger.log_info(f"Attempting to close all open trades for {self.strategy.instrument}...")
            all_open_trades = self.oanda_client.get_open_trades()
            open_trades = self._filter_trades_by_instrument(all_open_trades)

            if not open_trades:
                self.logger.log_info(f"No open trades to close for {self.strategy.instrument}")
                if len(all_open_trades) > 0:
                    self.logger.log_info(f"Found {len(all_open_trades)} open trades for other instruments - leaving them open")
                return True

            self.logger.log_info(f"Found {len(open_trades)} open trades to close")

            # Track success/failure for each trade
            success_count = 0
            failure_count = 0

            for trade in open_trades:
                try:
                    self.logger.log_info(f"Closing trade {trade.tradeID}...")
                    closed_trade = self.oanda_client.close_trade(trade.tradeID)

                    if closed_trade:
                        # Units check is now more flexible during shutdown
                        if closed_trade["units"] != trade.units and not is_shutdown:
                            self.logger.log_warning(
                                f"Units mismatch when closing trade {trade.tradeID} - "
                                f"Closed: {closed_trade['units']}, Original: {trade.units}"
                            )

                        # Create trade history row
                        final_trade_history_row = TradeHistoryRow(
                            tradeID=closed_trade["tradeID"],
                            status=closed_trade["status"],
                            units=float(closed_trade["units"]),
                            realizedPL=float(closed_trade["realizedPL"]),
                            halfSpreadCost=float(closed_trade["halfSpreadCost"]),
                            closeTime=closed_trade["closeTime"],
                            type=trade.type,
                            instrument=trade.instrument,
                            price=trade.price,
                            openTime=trade.openTime,
                            initialMarginRequired=trade.initialMarginRequired,
                            commission=trade.commission,
                        )

                        # Update risk management metrics with realized P&L
                        realized_pl = float(closed_trade["realizedPL"])
                        self.logger.log_info(f"Trade {trade.tradeID} closed with realized P&L: {realized_pl}")

                        # Update daily loss tracker if negative P&L
                        if realized_pl < 0:
                            self._update_daily_loss(realized_pl)

                        # Update total profit and loss trackers
                        self._update_total_pnl(realized_pl)

                        # Update Firebase
                        try:
                            self.firebase_client.update_open_trades([final_trade_history_row])
                        except Exception as firebase_error:
                            # Don't let Firebase errors stop us during shutdown
                            self.logger.log_error(firebase_error, f"Failed to update trade history for {trade.tradeID} in Firebase")
                            if not is_shutdown:
                                raise

                        success_count += 1
                        self.logger.log_info(f"Successfully closed trade {trade.tradeID}")
                    else:
                        # Check if trade was already closed by fast loop or automatically
                        if hasattr(self, 'fast_update_loop') and self.fast_update_loop and hasattr(self.fast_update_loop, 'was_trade_processed_by_fast_loop'):
                            if self.fast_update_loop.was_trade_processed_by_fast_loop(trade.tradeID):
                                self.logger.log_info(f"Trade {trade.tradeID} was already processed by fast loop, counting as success")
                                success_count += 1
                                continue

                        failure_count += 1
                        error_msg = f"Failed to close trade {trade.tradeID} - trade may already be closed"
                        self.logger.log_warning(error_msg)

                        if not is_shutdown:
                            self.firebase_client.append_user_log(
                                f"⚠️ Trade {trade.tradeID} could not be closed - may already be closed"
                            )
                            # Don't raise exception for trades that might already be closed
                            # raise Exception(f"Failed to close trade {trade.tradeID}")
                except Exception as e:
                    failure_count += 1
                    self.logger.log_error(e, f"Error closing trade {trade.tradeID}")

                    if not is_shutdown:
                        # During normal operation, propagate the error
                        self.firebase_client.append_user_log(
                            f"❌ Error closing trade {trade.tradeID}: {str(e)}"
                        )
                        raise

            # Log summary
            if success_count > 0 and failure_count == 0:
                self.logger.log_info(f"All {success_count} open trades closed successfully")
                self.firebase_client.append_user_log(
                    f"🟢 All {success_count} open trades closed successfully"
                )

                # Force a final update of risk management metrics
                self._update_risk_management_metrics()
                self.logger.log_info("Final risk management metrics updated after closing trades")

                return True
            elif success_count > 0 and failure_count > 0:
                self.logger.log_warning(f"Partially successful: Closed {success_count} trades, failed to close {failure_count} trades")
                self.firebase_client.append_user_log(
                    f"⚠️ Closed {success_count} trades, but failed to close {failure_count} trades"
                )

                # Force a final update of risk management metrics even with partial success
                self._update_risk_management_metrics()
                self.logger.log_info("Final risk management metrics updated after closing trades")

                return is_shutdown  # Only return True during shutdown
            elif failure_count > 0:
                self.logger.log_error(Exception(f"Failed to close all {failure_count} trades"), "Failed to close trades")
                self.firebase_client.append_user_log(
                    f"❌ Failed to close all {failure_count} trades"
                )
                return False

            return True

        except Exception as e:
            self.logger.log_error(e, "Error in close_and_update_open_trades")
            if not is_shutdown:
                self.firebase_client.append_user_log(
                    "❌ Error while closing trades"
                )
                raise
            return False

    def _check_stale_data(self, market_data: dict, timeframe: str) -> dict:
        """
        Check if market data is stale and should pause trading.

        Args:
            market_data: Market data from provider
            timeframe: Trading timeframe (e.g., '1m', '5m')

        Returns:
            dict: {
                "is_stale": bool,
                "message": str,
                "latest_candle_time": str,
                "age_minutes": float,
                "threshold_minutes": int
            }
        """
        try:
            # Extract timeframe multiplier (e.g., '1m' -> 1, '5m' -> 5)
            timeframe_minutes = 1
            if timeframe.endswith('m'):
                timeframe_minutes = int(timeframe[:-1])
            elif timeframe.endswith('h'):
                timeframe_minutes = int(timeframe[:-1]) * 60

            # Set staleness threshold based on timeframe
            # For 1m: 2 minutes (temporarily lowered for testing), for 5m: 15 minutes, etc.
            threshold_minutes = max(2, timeframe_minutes * 3)

            # Check if we have market data
            if market_data.get("status") != "success" or not market_data.get("candles"):
                return {
                    "is_stale": True,
                    "message": "No market data available from provider",
                    "latest_candle_time": None,
                    "age_minutes": None,
                    "threshold_minutes": threshold_minutes
                }

            # Get latest candle
            candles = market_data["candles"]
            if not candles:
                return {
                    "is_stale": True,
                    "message": "No candles available in market data",
                    "latest_candle_time": None,
                    "age_minutes": None,
                    "threshold_minutes": threshold_minutes
                }

            # Get the latest candle timestamp
            latest_candle = candles[-1]
            latest_timestamp = latest_candle.get("time")

            if not latest_timestamp:
                return {
                    "is_stale": True,
                    "message": "Latest candle has no timestamp",
                    "latest_candle_time": None,
                    "age_minutes": None,
                    "threshold_minutes": threshold_minutes
                }

            # Convert timestamp to datetime
            from datetime import datetime, timezone
            if isinstance(latest_timestamp, (int, float)):
                latest_time = datetime.fromtimestamp(latest_timestamp, tz=timezone.utc)
            else:
                # Assume it's already a datetime or string
                if isinstance(latest_timestamp, str):
                    latest_time = datetime.fromisoformat(latest_timestamp.replace('Z', '+00:00'))
                else:
                    latest_time = latest_timestamp

            # Calculate age in minutes
            current_time = datetime.now(timezone.utc)
            age_seconds = (current_time - latest_time).total_seconds()
            age_minutes = age_seconds / 60

            # Check if data is stale
            is_stale = age_minutes > threshold_minutes

            if is_stale:
                message = f"Market data is {age_minutes:.1f} minutes old (threshold: {threshold_minutes} minutes). Bot will resume when fresh data is available."
            else:
                message = f"Market data is fresh ({age_minutes:.1f} minutes old)"

            return {
                "is_stale": is_stale,
                "message": message,
                "latest_candle_time": latest_time.isoformat(),
                "age_minutes": age_minutes,
                "threshold_minutes": threshold_minutes
            }

        except Exception as e:
            self.logger.log_error(e, "Error checking stale data")
            return {
                "is_stale": True,
                "message": f"Error checking data freshness: {str(e)}",
                "latest_candle_time": None,
                "age_minutes": None,
                "threshold_minutes": 5
            }
